<p-panel header="Chi tiết kế hoạch dự án">
    <ng-template pTemplate="icons">
        <div class="tw-flex tw-items-center tw-gap-2 tw-mr-2">
            <p-button label="Thiết lập ngày làm việc" size="small" severity="info" (click)="openWorkingDay()"></p-button>

            <app-popup
                header="Xuất chi tiết kế hoạch dự án"
                severity="success"
                label="Xuất Excel"
                (onSubmit)="onOpenExportExcel()"
                typePopup="download"
            ></app-popup>
        </div>
    </ng-template>

    <!-- <ng-template> -->
    <app-from [formGroup]="formArray">
        <p-table [value]="visibleTasks" [scrollable]="true" [resizableColumns]="true">
            <ng-template pTemplate="header">
                <tr>
                    <th>STT</th>
                    <th style="min-width: 16rem">Task Name</th>
                    <th style="min-width: 16rem">Timeline</th>
                    <th>Duration (days)</th>
                    <th style="min-width: 12rem">Predecessor</th>
                    <th style="min-width: 15rem">Người phụ trách</th>
                    <th>Tiến độ (%)</th>
                    <th style="min-width: 15rem">Trạng thái</th>
                    <th style="min-width: 15rem">Ghi chú</th>
                    <th>Thao tác</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-control let-rowIndex="rowIndex">
                <tr [formGroupName]="rowIndex" [ngClass]="getRowClassBySTT(control)">
                    <td>
                        <app-form-item label="">{{ control.get('displaySTT')?.value }}</app-form-item>
                    </td>

                    <td>
                        <app-form-item label="" validateTrigger="touched">
                            <input type="text" class="tw-w-full" pInputText maxlength="200" formControlName="name" />
                        </app-form-item>
                    </td>
                    <td>
                        <app-form-item label="" validateTrigger="touched">
                            <p-calendar
                                #calendarRef
                                formControlName="timeline"
                                selectionMode="range"
                                [readonlyInput]="true"
                                class="tw-w-full"
                                [showIcon]="true"
                                appendTo="body"
                                dateFormat="dd/mm/yy"
                                placeholder="Từ... - Đến..."
                                (onSelect)="onDateRangeSelect(rowIndex, calendarRef)"
                            >
                            </p-calendar>
                        </app-form-item>
                    </td>
                    <td style="text-align: center">
                        <app-form-item>{{ control.get('duration')?.value }}</app-form-item>
                    </td>
                    <td>
                        <app-form-item label="">
                            <input
                                type="text"
                                class="tw-w-full"
                                pInputText
                                formControlName="predecessor"
                                [readonly]="formArray.at(rowIndex)?.get('level')?.value > 1"
                                (keypress)="allowOnlyPredecessorChars($event)"
                                appendTo="body"
                            />
                            <div
                                *ngIf="formArray.at(rowIndex)?.get('predecessor')?.errors?.invalidSTT && formArray.at(rowIndex)?.get('predecessor')?.touched"
                                class="tw-text-red-500 tw-text-sm"
                            >
                                Vui lòng nhập STT hợp lệ
                            </div>
                        </app-form-item>
                    </td>
                    <td>
                        <app-form-item label="">
                            <app-combobox-nonRSQL
                                #userFilter
                                (panelShow)="handlePanelShow(userFilter)"
                                [fetchOnInit]="false"
                                type="select-one"
                                formControlName="assignee"
                                fieldValue="id"
                                fieldLabel="fullName"
                                url="/auth/api/users/simple-search"
                                param="query"
                                placeholder="Chọn giá trị"
                                [additionalParams]="{ size: 100 }"
                            >
                            </app-combobox-nonRSQL>
                        </app-form-item>
                    </td>
                    <td>
                        <input
                            type="text"
                            class="tw-w-full"
                            pInputText
                            formControlName="progress"
                            (keypress)="allowOnlyPredecessorChars($event)"
                            (blur)="validateProgress(rowIndex)"
                            maxlength="3"
                            [readonly]="isProgressReadOnly(rowIndex)"
                        />
                    </td>
                    <td>
                        <app-form-item label="">
                            <p-dropdown
                                [options]="statusOptions"
                                appendTo="body"
                                class="tw-w-full"
                                formControlName="status"
                                optionLabel="label"
                                optionValue="value"
                                (onChange)="onStatusChange(rowIndex)"
                            ></p-dropdown>
                        </app-form-item>
                    </td>
                    <td>
                        <app-form-item label="">
                            <input type="text" pInputText class="tw-w-full" maxlength="500" formControlName="note" />
                        </app-form-item>
                    </td>
                    <td>
                        <button
                            pButton
                            type="button"
                            icon="pi pi-ellipsis-h"
                            class="p-button-text p-button-sm"
                            (click)="onMenuClick($event, rowIndex, menu)"
                        ></button>
                        <p-menu #menu [popup]="true" [model]="menuItems" appendTo="body"></p-menu>
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </app-from>
    <p-button label="Thêm task" size="small" severity="info" (click)="addTask()"></p-button>
</p-panel>

<app-popup #WorkingDayPopup header="THIẾT LẬP NGÀY LÀM VIỆC" [isButtonVisible]="false" dialogWidth="65vw" [showConfirmButton]="false">
    <div class="workday-dialog-content tw-grid lg:tw-grid-cols-[200px_1fr_200px] tw-grid-cols-1 tw-gap-4">
        <div class="legend-section">
            <h3>Bảng chú thích</h3>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="calendar-box working">01</div>
                    <span>Working</span>
                </div>
                <div class="legend-item">
                    <div class="calendar-box nonworking">01</div>
                    <span>Nonworking</span>
                </div>
            </div>
        </div>
        <div class="calendar-section tw-flex tw-flex-col tw-items-center tw-text-center tw-w-full">
            <h3>Chọn ngày để xem thiết lập</h3>
            <!-- <div class="tw-w-full"> -->
            <p-calendar [(ngModel)]="selectedDates" class="tw-w-full" [inline]="true" [panelStyleClass]="'compact-week-calendar'" [firstDayOfWeek]="0">
            </p-calendar>
            <!-- </div> -->
            <div class="tw-text-gray-500">Lưu ý: Default Weekend = Nonworking</div>
        </div>
        <div class="detail-section">
            <h3 class="tw-flex tw-flex-col tw-items-center tw-text-center tw-w-full">Nội dung thiết lập</h3>
            <span> </span>
        </div>
    </div>
    <div class="workday-dialog-content">
        <h3>Bảng thiết lập</h3>
        <app-from [formGroup]="settingsForm">
            <p-table [value]="settingsForm.controls" [scrollable]="true" [resizableColumns]="true" [tableStyle]="{ 'table-layout': 'auto', width: '100%' }">
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 4rem; text-align: center">STT</th>
                        <th style="width: 13rem; text-align: center">Nội dung</th>
                        <th style="width: 9rem; text-align: center">Loại</th>
                        <th style="width: 10rem; text-align: center">Khoảng thời gian</th>
                        <th style="width: 10rem; text-align: center">Ngày cụ thể</th>
                        <th style="width: 3rem; text-align: center">
                            <button pButton type="button" icon="pi pi-plus" class="p-button-secondary p-button-sm-add"></button>
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-setting let-rowIndex="rowIndex">
                    <tr [formGroup]="setting">
                        <td style="text-align: center">{{ rowIndex + 1 }}</td>
                        <td>
                            <app-form-item label="">
                                <input pInputText formControlName="content" type="text" />
                            </app-form-item>
                        </td>
                        <td>
                            <app-form-item label="">
                                <p-dropdown
                                    formControlName="dayType"
                                    [options]="dayTypes"
                                    placeholder="Chọn giá trị"
                                    class="tw-w-full"
                                    optionLabel="label"
                                    optionValue="value"
                                    appendTo="body"
                                ></p-dropdown>
                            </app-form-item>
                        </td>
                        <td>
                            <div class="tw-flex tw-items-center tw-gap-2">
                                <p-checkbox formControlName="enableDateRange" binary="true" (onChange)="onDateRangeToggle(setting)"></p-checkbox>
                                <app-form-item label="">
                                    <p-calendar
                                        formControlName="rangeDates"
                                        selectionMode="range"
                                        [readonlyInput]="true"
                                        [showIcon]="true"
                                        appendTo="body"
                                        dateFormat="dd/mm/yy"
                                        placeholder="Từ... - Đến..."
                                    ></p-calendar>
                                </app-form-item>
                            </div>
                        </td>
                        <td>
                            <div class="tw-flex tw-items-center tw-gap-2">
                                <p-checkbox formControlName="enableSpecificDate" binary="true" (onChange)="onDateRangeToggle(setting)"></p-checkbox>

                                <p-calendar
                                    formControlName="specificDate"
                                    [showIcon]="true"
                                    placeholder="Chọn ngày"
                                    dateFormat="dd/mm/yy"
                                    appendTo="body"
                                ></p-calendar>
                            </div>
                        </td>
                        <td>
                            <div class="tw-flex tw-items-center tw-justify-center tw-space-x-2 text-center">
                                <button
                                    pButton
                                    type="button"
                                    icon="pi pi-minus"
                                    style="color: white"
                                    class="p-button-sm-add p-button-danger tw-text-white"
                                    (click)="deleteRow(i)"
                                ></button>
                            </div>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </app-from>
    </div>
    <div class="tw-absolute tw-right-[90px]" style="bottom: 20px">
        <button pButton label="Áp dụng" class="p-button-sm p-button-secondary" (click)="onApply()"></button>
    </div>
</app-popup>
