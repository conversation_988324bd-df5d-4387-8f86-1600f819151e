import { Component, Input, Output, EventEmitter, On<PERSON>nit, On<PERSON>estroy, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { BorService } from 'src/app/services/ptm/bor/bor.service';
import { MessageService } from 'primeng/api';
import { Observable, Subject, takeUntil } from 'rxjs';
import { Bor, ManDetail, LineDetail, MaterialDetail } from 'src/app/models/interface/ptm/bor';
import { TabSharedStateService } from 'src/app/services/ptm/manufacturing-technology-records/tab-shared-state/tab-shared-state.service';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { TAB_TYPE } from 'src/app/models/constant/ptm';

@Component({
    selector: 'app-bor',
    standalone: true,
    imports: [CommonModule, FormsModule, InputTextareaModule, InfoShareComponent, FormCustomModule, PanelModule, TableModule, ButtonModule],
    templateUrl: './bor.component.html',
    styleUrls: ['./bor.component.scss'],
})
export class BorComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() onSubmit!: () => void; // Callback được truyền
    @Input() currentProduct: any;

    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    private destroy$ = new Subject<void>();
    oldBor: Bor = {
        reviewerIds: [],
        listMan: [],
        listLine: [],
        listMaterial: [],
    };
    instructionId: number;
    name: string;
    detailBor: any;
    mode: 'view' | 'create' | 'edit' = 'create';
    tabType = TAB_TYPE.bor;
    formGroup: FormGroupCustom<Bor>;
    isCreatingInstruction = false;

    constructor(
        private fb: FormBuilder,
        private borService: BorService,
        private messageService: MessageService,
        private tabSharedState: TabSharedStateService,
    ) {}

    ngOnDestroy(): void {
        console.log('🧹 [BorComponent] Unmounted');
    }
    ngOnInit(): void {
        console.log('🧹 [BorComponent] Init');
        this.initForm(this.oldBor);
        this.name = 'BOR-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
        this.tabSharedState
            .getProductInstructionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((id) => {
                if (id) {
                    this.instructionId = id;
                    this.getBor();
                } else {
                    this.getBor();
                }
            });
        this.tabSharedState
            .getMode$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((mode) => {
                this.mode = mode;
            });
        this.tabSharedState
            .getProductVersionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((verId) => {
                this.formGroup.patchValue({
                    versionId: verId,
                });
            });

        this.tabSharedState
            .getPhase$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((phase) => {
                this.formGroup.patchValue({
                    phase: phase,
                });
            });
    }

    initForm(data: Bor | null) {
        this.formGroup = new FormGroupCustom<Bor>(this.fb, {
            listMan: new FormArrayCustom(this.initFormContact(data?.listMan || [])),
            listLine: new FormArrayCustom(this.initFormContact(data?.listLine || [])),
            listMaterial: new FormArrayCustom(this.initFormContact(data?.listMaterial || [])),
            reviewerIds: [data?.reviewerIds],
        });
    }

    get listMan(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listMan') as FormArrayCustom<FormGroup>;
    }

    get listLine(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listLine') as FormArrayCustom<FormGroup>;
    }

    get listMaterial(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listMaterial') as FormArrayCustom<FormGroup>;
    }

    initFormContact(items: any[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    manBomId: [item?.manBomId],
                    instructionId: [item?.instructionId],
                    pfmeaId: [item?.pfmeaId],
                    workStandardId: [item?.workStandardId],
                    description: [item?.description, Validators.required],
                    unit: [item?.unit, Validators.required],
                    section: [item?.section, Validators.required],
                    quantity: [item?.quantity, Validators.required],
                    reference: [item?.reference],
                    materialType: [item?.materialType, Validators.required],
                    consumptionRate: [item?.consumptionRate],
                    note: [item?.note],
                    action: [item?.action],
                }),
        );
    }

    addItem() {}

    handleApproverChange(event: any) {
        this.formGroup.patchValue({
            reviewerIds: event.map((item: any) => item.id),
        });
    }

    getBor() {
        this.borService.getBor(this.instructionId).subscribe({
            next: (res) => {
                this.detailBor = res;
                this.initForm(this.detailBor);
            },
            error: () => {},
        });
    }

    handlePreview() {
        const payload = {
            tabType: this.tabType,
        };
        this.borService.previewBor(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Gửi review thành công',
                });
                this.getBor();
            },
            error: () => {},
        });
    }

    handleComplete() {
        const payload = {
            tabType: this.tabType,
            approvalStatus: 8,
        };
        this.borService.comfirmBor(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Phê duyệt thành công',
                });
                this.getBor();
            },
            error: () => {},
        });
    }

    handleReject() {
        const payload = {
            tabType: this.tabType,
            approvalStatus: 4,
        };
        this.borService.comfirmBor(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Từ chối thành công',
                });
                this.getBor();
            },
            error: () => {},
        });
    }

    handleSubmit() {}
}
