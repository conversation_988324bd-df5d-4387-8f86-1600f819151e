import { Component, Input, Output, EventEmitter, On<PERSON>nit, On<PERSON>estroy, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InfoShareComponent } from '../info-share/info-share.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { FormComponent } from 'src/app/shared/form-module/form-base/form.component';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { AbstractControlCustom, FormGroupCustom } from 'src/app/shared/form-module/from-group.custom';
import { FormArrayCustom } from 'src/app/shared/form-module/from-array.custom';
import { <PERSON><PERSON><PERSON>, ManBomDetail } from 'src/app/models/interface/ptm/manbom';
import { ManBomService } from 'src/app/services/ptm/manbom/manbom.service';
import { Observable, Subject, takeUntil } from 'rxjs';
import { InputTextModule } from 'primeng/inputtext';
import { MANBOM_PROCESS_TYPE, MANBOM_MATERIAL_TYPE, TAB_TYPE } from 'src/app/models/constant/ptm';
import { DropdownModule } from 'primeng/dropdown';
import { TabSharedStateService } from 'src/app/services/ptm/manufacturing-technology-records/tab-shared-state/tab-shared-state.service';
import { MessageService } from 'primeng/api';

@Component({
    selector: 'app-manbom',
    standalone: true,
    templateUrl: './manbom.component.html',
    styleUrls: ['./manbom.component.scss'],
    imports: [
        CommonModule,
        FormsModule,
        InputTextareaModule,
        InfoShareComponent,
        FormCustomModule,
        PanelModule,
        TableModule,
        ButtonModule,
        InputTextModule,
        DropdownModule,
    ],
})
export class ManbomComponent implements OnInit, OnDestroy {
    // 🧠 INPUTS từ cha truyền xuống
    @Input() title: string = '';
    @Input() content: string = '';
    @Input() config: { type: string } = { type: 'default' };
    @Input() items: string[] = [];
    @Input() editable: boolean = true;
    @Input() isSaving!: Observable<boolean>;
    @Input() currentProduct: any;
    @Input() productInstructionId: number;
    @ViewChild('form', { static: false }) formComponent!: FormComponent;
    processType = MANBOM_PROCESS_TYPE;
    materialType = MANBOM_MATERIAL_TYPE;
    oldManBom: ManBom = {
        reviewerIds: [],
        listManBom: [],
    };
    instructionId: number;
    name: string;
    detailManBom: any;
    mode: 'view' | 'create' | 'edit' = 'create';
    isCreatingInstruction = false;
    tabType = TAB_TYPE.manbom;
    private destroy$ = new Subject<void>();
    // 📤 OUTPUTS emit về cha
    @Output() submitted = new EventEmitter<any>();

    ngOnDestroy(): void {
        console.log('🧹 [ManbomComponent] Unmounted');
    }
    ngOnInit(): void {
        console.log('🧹 [ManbomComponent] Init');
        this.instructionId = this.productInstructionId;
        this.initForm(this.oldManBom);
        this.name = 'PFD-' + this.currentProduct?.tradeName + '-' + this.currentProduct?.vnptManPn;
        this.tabSharedState
            .getProductInstructionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((id) => {
                if (id) {
                    this.instructionId = id;
                    this.getManBom();
                } else {
                    this.getManBom();
                }
            });

        this.tabSharedState
            .getProductVersionId$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((verId) => {
                this.formGroup.patchValue({
                    versionId: verId,
                });
            });

        this.tabSharedState
            .getPhase$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((phase) => {
                this.formGroup.patchValue({
                    phase: phase,
                });
            });

        this.tabSharedState
            .getMode$()
            .pipe(takeUntil(this.destroy$))
            .subscribe((mode) => {
                this.mode = mode;
            });
    }
    formGroup: FormGroupCustom<ManBom>;
    constructor(
        private fb: FormBuilder,
        private manBomService: ManBomService,
        private tabSharedState: TabSharedStateService,
        private messageService: MessageService,
    ) {
        this.initForm(this.oldManBom);
    }

    initForm(data: ManBom | null) {
        this.formGroup = new FormGroupCustom<ManBom>(this.fb, {
            listManBom: new FormArrayCustom(this.initFormContact(data?.listManBom || [])),
            reviewerIds: [data?.reviewerIds],
        });
    }

    get manBom(): FormArrayCustom<FormGroup> {
        return this.formGroup.get('listManBom') as FormArrayCustom<FormGroup>;
    }

    initFormContact(items: ManBomDetail[]): FormGroup[] {
        return items.map(
            (item) =>
                new FormGroupCustom(this.fb, {
                    id: [item?.id],
                    instructionId: [item?.instructionId],
                    pfmeaId: [item?.pfmeaId],
                    workStandardId: [item?.workStandardId],
                    description: [item?.description, Validators.required],
                    unit: [item?.unit, Validators.required],
                    section: [item?.section, Validators.required],
                    quantity: [item?.quantity, Validators.required],
                    reference: [item?.reference],
                    materialType: [item?.materialType, Validators.required],
                    consumptionRate: [item?.consumptionRate],
                    note: [item?.note],
                    action: [item?.action],
                }),
        );
    }
    handleApproverChange(event: any) {
        this.formGroup.patchValue({
            reviewerIds: event.map((item: any) => item.id),
        });
    }

    getManBom() {
        this.manBomService.getManBom(this.instructionId).subscribe({
            next: (res) => {
                this.detailManBom = res;
            },
            error: () => {},
        });
    }

    addItem() {
        let newItem = null;
        newItem = new FormGroupCustom(this.fb, {
            id: [null],
            instructionId: [null],
            pfmeaId: [null],
            workStandardId: [null],
            description: [null, Validators.required],
            unit: [null, Validators.required],
            section: [null, Validators.required],
            quantity: [null, Validators.required],
            reference: [null],
            materialType: [null, Validators.required],
            consumptionRate: [null],
            note: [null],
            action: [1],
        });
        this.manBom.push(newItem);
    }

    removeItem(index: number) {
        if (this.manBom.at(index).get('id')?.value === null) {
            this.manBom.removeAt(index);
        } else {
            this.manBom.at(index).get('action')?.setValue(3);
        }
    }

    handlePreview() {
        const payload = {
            tabType: this.tabType,
        };
        this.manBomService.previewManBom(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Gửi review thành công',
                });
                this.getManBom();
            },
            error: () => {},
        });
    }

    handleComplete() {
        const payload = {
            tabType: this.tabType,
            approvalStatus: 8,
        };
        this.manBomService.comfirmManBom(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Phê duyệt thành công',
                });
                this.getManBom();
            },
            error: () => {},
        });
    }

    handleReject() {
        const payload = {
            tabType: this.tabType,
            approvalStatus: 4,
        };
        this.manBomService.comfirmManBom(payload, this.instructionId).subscribe({
            next: (res) => {
                this.messageService.add({
                    key: 'app-alert',
                    severity: 'success',
                    summary: 'Thành công',
                    detail: 'Từ chối thành công',
                });
                this.getManBom();
            },
            error: () => {},
        });
    }

    handleSubmit() {
        if (this.formGroup.invalid) return;
        const payloadData = {
            details: this.manBom.controls.map((group) => {
                return {
                    id: group.get('id')?.value,
                    pfmeaId: group.get('pfmeaId')?.value,
                    workStandardId: group.get('workStandardId')?.value,
                    description: group.get('description')?.value,
                    unit: group.get('unit')?.value,
                    section: group.get('section')?.value,
                    quantity: group.get('quantity')?.value,
                    reference: group.get('reference')?.value,
                    materialType: group.get('materialType')?.value,
                    consumptionRate: group.get('consumptionRate')?.value,
                    note: group.get('note')?.value,
                    action: group.get('action')?.value,
                };
            }),
            versionId: this.formGroup.get('versionId')?.value,
            phase: this.formGroup.get('phase')?.value,
            reviewerIds: this.formGroup.get('reviewerIds')?.value,
        };

        this.manBomService.create(payloadData, this.instructionId).subscribe({
            next: (res) => {
                if (this.instructionId !== 0) {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Cập nhật manbom thành công',
                    });
                } else {
                    this.messageService.add({
                        key: 'app-alert',
                        severity: 'success',
                        summary: 'Thành công',
                        detail: 'Tạo manbom thành công',
                    });
                }
                this.getManBom();
            },
            error: () => {},
            complete: () => {},
        });
    }
    onSubmit() {
        if (this.formGroup.invalid) return;
        this.submitted.emit(this.formGroup.value);
    }
}
