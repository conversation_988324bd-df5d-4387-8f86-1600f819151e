import { Component, inject, OnIni<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Template<PERSON><PERSON>, ViewChildren, QueryList, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SubHeaderComponent } from 'src/app/shared/components/sub-header/sub-header.component';
import { ProductInfoComponent } from 'src/app/modules/pms/components/product-info/product-info.component';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { DesignProfileComponent } from 'src/app/modules/pms/product-file/edit/components/design-profile/design-profile.component';
import { ProductionProfileComponent } from 'src/app/modules/pms/product-file/edit/components/production-profile/production-profile.component';
import { QualityProfileComponent } from 'src/app/modules/pms/product-file/edit/components/quality-profile/quality-profile.component';
import { HasAnyAuthorityDirective } from 'src/app/shared/directives/has-any-authority.directive';
import { WizardComponent } from 'src/app/shared/components/wizard/wizard.component';
import { InputTextModule } from 'primeng/inputtext';
import { RouterLink } from '@angular/router';
import { StepsModule } from 'primeng/steps';
import { ITEMS_STEP, LIFECYCLE_STAGE_DOC_MAP, LIFECYCLE_STAGE_PRODUCT_MAP, STATUS_MAP } from 'src/app/models/constant/pms';
import { PanelModule } from 'primeng/panel';
import { ProductDetail, ProductSoftware, ProductRecordVersion, DropdownOption, VersionRecord } from 'src/app/models/interface/pms';
import { BehaviorSubject, distinctUntilChanged, forkJoin, from, map, Observable, Subject, takeUntil } from 'rxjs';
import { TableCommonModule } from 'src/app/shared/table-module/table.common.module';
import { ProductFileService } from 'src/app/services/pms/product-file/product-file.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfirmationService } from 'primeng/api';
import { TableModule } from 'primeng/table';
import { EventBusService } from 'src/app/services/eventBus.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { DialogModule } from 'primeng/dialog';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ProductFileStateService } from '../../../../services/pms/product-file/product-file-state.service';
import { environment } from '../../../../../environments/environment';
import { FilterChangeEvent } from 'src/app/shared/table-module/custom-filter-table/custom-filter-table.component';
import { ProcessFlowComponent } from '../components/process-flow/process-flow.component';
import { TabViewComponent } from 'src/app/shared/components/tab-view-dynamic/tab-view-dynamic.component';
import { RoutingComponent } from '../components/routing/routing.component';
import { TccComponent } from '../components/tcc/tcc.component';
import { PfmeaComponent } from '../components/pfmea/pfmea.component';
import { ManbomComponent } from '../components/manbom/manbom.component';
import { BorComponent } from '../components/bor/bor.component';
import { SopApplicationComponent } from '../components/sop-application/sop-application.component';
import { OtherDocsComponent } from '../components/other-docs/other-docs.component';
import { CLDocsComponent } from '../components/CL-docs/CL-docs.component';
import { SopBaseComponent } from '../components/sop-base/sop-base.component';
import { WorkStandardComponent } from '../components/work-standard/work-standard.component';
import { MfgTechRecordService } from 'src/app/services/ptm/manufacturing-technology-records/mfg-tech-records.service';
import { TabSharedStateService } from 'src/app/services/ptm/manufacturing-technology-records/tab-shared-state/tab-shared-state.service';

interface Item {
    label: string;
    value: any;
}

@Component({
    selector: 'app-mfg-tech-records.edit',
    standalone: true,
    imports: [
        SubHeaderComponent,
        ProductInfoComponent,
        ButtonModule,
        DropdownModule,
        FormsModule,
        PopupComponent,
        FormCustomModule,
        HasAnyAuthorityDirective,
        CommonModule,
        InputTextModule,
        RouterLink,
        StepsModule,
        TabViewComponent,
        DesignProfileComponent,
        ProductionProfileComponent,
        QualityProfileComponent,
        WizardComponent,
        PanelModule,
        TableCommonModule,
        DialogModule,
        InputTextareaModule,
        TableModule,
        ProcessFlowComponent,
        RoutingComponent,
        TccComponent,
        PfmeaComponent,
        ManbomComponent,
        BorComponent,
    ],
    templateUrl: './mfg-tech-records.edit.component.html',
    styleUrls: ['./mfg-tech-records.edit.component.scss'],
    providers: [ProductFileService, TabSharedStateService],
})
export class MfgTechRecordsEditComponent implements OnInit, OnDestroy {
    //inject deps
    productFileStateService = inject(ProductFileStateService);
    private fb = inject(FormBuilder);

    public STORAGE_BASE_URL = environment.STORAGE_BASE_URL;
    //state
    isSaving: boolean = false;
    /** Cờ xác định đã gọi submit chưa (để guard 1 lần duy nhất) */
    private hasSubmitted = false;
    filterForm!: FormGroup;
    private productVersionOptions = new BehaviorSubject<DropdownOption[]>([]);
    public productVersionOptions$: Observable<DropdownOption[]> = this.productVersionOptions.asObservable();
    private stageOptions = new BehaviorSubject<DropdownOption[]>([]);
    public stageOptions$: Observable<DropdownOption[]> = this.stageOptions.asObservable();
    productSoftwareOptions: ProductSoftware[] = [];
    activeIndex: number = 0;
    itemsStep = ITEMS_STEP;
    currentProduct?: ProductDetail;
    version!: ProductRecordVersion;
    mode!: 'create' | 'view' | 'edit';
    selectedProduct: any;
    selectedPartNumber: any;
    selectedVersion: any;
    resultUrl: string | null = null;
    dataVersionClone: any[];
    records: VersionRecord[] = [];
    isNotClone: boolean = false;
    showStageFilter = true;
    shouldUseVersionRef: boolean = false;
    isSearching = false;
    isButtonVisible: boolean = false;
    isUploading = false;
    productInstructionId: number = 0;
    productInstructionName: string = '';
    productId: number = 0;
    productRecordId: number = 0;
    versionRef: number;
    tabs = [];
    currentTab = 0;
    versionOptions: { label: string; value: any }[] = [];
    selectedLifecycleStage: Item = {
        label: 'Prototype',
        value: 1,
    };
    designMaterials: any = {
        productRecordSelected: null,
        listVersion: [],
    };

    stageMap = LIFECYCLE_STAGE_DOC_MAP;
    statusMap = STATUS_MAP;
    optionsLifecycleStage: Item[] = Object.entries(LIFECYCLE_STAGE_DOC_MAP)
        .filter(([key, label]) => Number(key) !== 4)
        .map(([key, label]) => ({
            value: Number(key),
            label: label,
        }));
    itemsHeader = [
        { label: 'Quản lý hồ sơ CNSX' },
        {
            label: 'Danh sách hồ sơ CNSX',
            url: '/ptm/manufacturing-technology-records',
        },
        { label: 'Tạo mới' },
    ];
    private destroy$ = new Subject<void>();
    @ViewChild(TabViewComponent) tabView!: TabViewComponent;
    @ViewChild('processFlowTpl', { static: true })
    processFlowTpl!: TemplateRef<any>;

    @ViewChild('routingTpl', { static: true })
    routingTpl!: TemplateRef<any>;
    // Lấy tất cả instance của ProcessFlow + Routing đang render
    @ViewChildren(ProcessFlowComponent)
    private pfComponents!: QueryList<ProcessFlowComponent>;

    @ViewChildren(RoutingComponent)
    private rtComponents!: QueryList<RoutingComponent>;

    @ViewChildren(TccComponent)
    private tccComponents!: QueryList<TccComponent>;

    @ViewChildren(PfmeaComponent)
    private pfmeaComponents!: QueryList<PfmeaComponent>;

    @ViewChildren(WorkStandardComponent)
    private workStandardComponent!: QueryList<WorkStandardComponent>;

    constructor(
        private route: ActivatedRoute,
        private bus: EventBusService,
        private router: Router,
        private alertService: AlertService,
        private confirmationService: ConfirmationService,
        private mtrs: MfgTechRecordService,
        private productFileService: ProductFileService,
        private tabSharedState: TabSharedStateService,
        private cdr: ChangeDetectorRef,
    ) {}

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    handleSubmitTab(val) {}

    handleProcessSubmit(val: any) {
        this.setValueProductInstructionId(val.id);
    }

    handleRoutingSubmit(val: any) {
        console.log('Submit từ Routing:', val);
    }

    handlePfmeaSubmit(val: any) {
        console.log('Submit từ Pfmea:', val);
    }

    handleWorkStandardSubmit(val: any) {
        console.log('Submit từ WorkStandard:', val);
    }

    setValueProductInstructionId(id: number) {
        this.productInstructionId = id;
        this.tabSharedState.setProductInstructionId(this.productInstructionId);
    }

    buildTabs() {
        this.tabs = [
            {
                id: 'process',
                label: 'Process Flow',
                component: ProcessFlowComponent,
                inputs: {
                    title: 'Process Flow',
                    currentProduct: this.currentProduct,
                    onSubmit: () => {},
                    content: '',
                    editable: true,
                    productVersionId: this.designMaterials?.productRecordSelected?.id,
                    phase: this.selectedLifecycleStage.value,
                    productInstructionId: this.productInstructionId,
                },
                outputs: {
                    submitted: (val: any) => this.handleProcessSubmit(val),
                    changed: (value: string) => {},
                    idProductInstruction: (id: number) => {},
                },
            },
            {
                id: 'routing',
                label: 'Routing',
                component: RoutingComponent,
                inputs: {
                    content: 'Dữ liệu điều phối',
                    title: 'Routing',
                    currentProduct: this.currentProduct,
                    productVersionId: this.designMaterials?.productRecordSelected?.id,
                    phase: this.selectedLifecycleStage.value,
                    productInstructionId: this.productInstructionId,
                },
                outputs: {
                    submitted: (val: any) => this.handleRoutingSubmit(val),
                },
            },
            {
                id: 'pfmea',
                label: 'PFMEA',
                component: PfmeaComponent,
                inputs: {
                    content: 'Dữ liệu điều phối',
                    productInstructionId: this.productInstructionId,
                    currentProduct: this.currentProduct,
                },
                outputs: {
                    submitted: (val: any) => this.handlePfmeaSubmit(val),
                },
                alwaysReload: true,
            },
            {
                id: 'work-standard',
                label: 'Work Standard',
                component: WorkStandardComponent,
                inputs: {
                    content: 'Dữ liệu kiểm tra',
                    productInstructionId: this.productInstructionId,
                    currentProduct: this.currentProduct,
                },
                outputs: {
                    submitted: (val: any) => this.handleWorkStandardSubmit(val),
                },
                alwaysReload: true,
            },
            {
                id: 'tcc',
                label: 'TCC',
                component: TccComponent,
                inputs: {
                    content: 'Dữ liệu TCC',
                },
            },
            {
                id: 'manbom',
                label: 'MANBOM',
                component: ManbomComponent,
                inputs: {
                    title: 'MANBOM',
                    content: 'Dữ liệu MANBOM',
                    productInstructionId: this.productInstructionId,
                    currentProduct: this.currentProduct,
                },
            },
            {
                id: 'bor',
                label: 'BOR',
                component: BorComponent,
                inputs: {
                    title: 'BOR',
                    content: 'Dữ liệu BOR',
                    currentProduct: this.currentProduct,
                },
            },
            {
                id: 'sop-base',
                label: 'SOP BASE',

                component: SopBaseComponent,
                inputs: {
                    product: this.currentProduct,
                },
                outputs: {
                    submitted: (val: any) => this.handleSubmitTab(val),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
            {
                id: 'sop-application',
                label: 'SOP Application',

                component: SopApplicationComponent,
                inputs: {
                    product: this.currentProduct,
                },
                outputs: {
                    submitted: (val: any) => this.handleSubmitTab(val),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
            {
                id: 'CL-docs',
                label: 'Bộ tài liệu CL',

                component: CLDocsComponent,
                inputs: {},
                outputs: {
                    submitted: (val: any) => this.setValueProductInstructionId(val.instructionInfoDto.instructionId),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
            {
                id: 'other-docs',
                label: 'Tài liệu khác',

                component: OtherDocsComponent,
                inputs: {},
                outputs: {
                    submitted: (val: any) => this.setValueProductInstructionId(val.instructionInfoDto.instructionId),
                    changed: (value: string) => {
                        console.log('Value changed:', value);
                    },
                },
            },
        ];
    }

    ngOnInit() {
        this.tabSharedState
            .getProductInstructionId$()
            .pipe(
                takeUntil(this.destroy$),
                distinctUntilChanged(), // chỉ emit khi id thay đổi
            )
            .subscribe((id) => {
                if (id) {
                    this.handleGetProductInstructionById(id);
                }
            });
        this.productId = +this.route.snapshot.paramMap.get('productId')!;
        this.productRecordId = +this.route.snapshot.paramMap.get('productRecordId')!;
        this.productInstructionId = +this.route.snapshot.paramMap.get('productInstructionId') || 0;

        if (this.productInstructionId) {
            this.tabSharedState.setProductInstructionId(this.productInstructionId);
        }

        this.route.queryParams.subscribe((params) => {
            const tabParam = Number(params['tab']);
            if (!isNaN(tabParam)) {
                this.currentTab = tabParam;
            }
        });
        forkJoin({
            product: this.getProductById(this.productId),
            listVersion: this.loadListVersion(this.productId),
            productRecord: this.getProductRecordById(this.productRecordId),
        }).subscribe({
            next: ({ product, listVersion, productRecord }) => {
                // Gán vào các biến như cũ
                this.currentProduct = product;
                this.designMaterials.listVersion = listVersion;
                this.designMaterials.productRecordSelected = productRecord;
                this.version = listVersion.filter((item) => {
                    if (item.id === this.productRecordId) {
                        return item;
                    }
                })[0];
                if (this.version) {
                    const statusCode = this.version.status;
                    const statusLabel = STATUS_MAP[statusCode];
                    const idx = ITEMS_STEP.findIndex((item) => item.name === statusLabel);
                    this.activeIndex = idx !== -1 ? idx : 0;
                    const stage = this.version.lifecycleStage;
                    this.selectedLifecycleStage = {
                        label: LIFECYCLE_STAGE_DOC_MAP[stage],
                        value: stage,
                    };
                } else {
                    this.selectedLifecycleStage = {
                        label: 'Prototype',
                        value: 1,
                    };
                }
                this.tabSharedState.setPhase(this.selectedLifecycleStage.value);
                this.tabSharedState.setProductVersionId(this.designMaterials?.productRecordSelected?.id);
                this.buildTabs();
            },
            error: (err) => {
                this.alertService.error('Lỗi khi tải dữ liệu', err.message);
            },
        });

        this.route.url.subscribe((segments) => {
            this.mode = segments[0].path as 'create' | 'edit' | 'view';
            this.tabSharedState.setMode(this.mode);
        });

        if (this.mode === 'create') {
            //    Trường hợp nhân bản từ danh sách hồ sơ
            this.isNotClone = true;
        }

        const lastLabel = this.mode === 'create' ? 'Tạo mới' : this.mode === 'edit' ? 'Chỉnh sửa' : 'Chi tiết';

        this.itemsHeader = [
            { label: 'Quản lý hồ sơ CNSX' },
            { label: 'Danh sách hồ sơ CNSX', url: '/ptm/manufacturing-technology-records' },
            { label: lastLabel },
        ];

        this.filterForm = this.fb.group({
            selectedProduct: [{ value: null, disabled: this.mode === 'view' }, Validators.required],
            selectedStage: [{ value: null, disabled: true }, Validators.required],
            selectedVersion: [{ value: null, disabled: true }, Validators.required],
            partNumber: [{ value: null, disabled: true }],
        });
    }

    handleGetProductInstructionById(id) {
        this.mtrs.getDetailProductInstruction(id).subscribe({
            next: (res) => {
                this.productInstructionName = res.versionName;
                if (this.mode === 'create') this.alertService.success('Thành công', `Thêm hồ sơ CNSX Ver ${res.versionName} thành công`);

                console.log('res', res);
            },
            error: (err) => {
                console.error('Delete error:', err);
            },
        });
    }

    onLifecycleStageChange(selected: any) {
        if (selected && selected.value) {
            this.tabSharedState.setPhase(selected.value);
        }
    }

    onTabChange(type: number) {
        console.log('onTabChange', type);
    }

    onFieldChanged(evt: { field: keyof ProductDetail; value: any; selected?: any }) {
        const { field, value, selected } = evt;
        console.log('selected', selected, 'field', field, 'value', value);
        if (!selected || !value) {
            this.designMaterials.productRecordSelected = null;
            return;
        }
        this.designMaterials.productRecordSelected = {
            ...selected,
            lifecycleStageText: this.stageMap[selected.lifecycleStage],
            statusText: this.statusMap[selected.status],
        };
    }

    loadListVersion(productId: number): Observable<any[]> {
        // Trả về Observable version list dạng array
        return this.mtrs.getVersionOptions(productId).pipe(
            // Nếu muốn map lại dữ liệu ngay tại đây:
            map((opts) =>
                opts.map((v) => ({
                    label: v.versionName,
                    value: v.id,
                    ...v,
                })),
            ),
        );
    }

    getProductRecordById(productRecordId: number): Observable<any> {
        return this.productFileService.getProductVersionById(productRecordId).pipe(
            map((productRecord) => ({
                ...productRecord,
                lifecycleStageText: this.stageMap[productRecord.lifecycleStage],
                statusText: this.statusMap[productRecord.status],
            })),
        );
    }

    getProductById(productId: number): Observable<any> {
        return this.productFileService.getDetailProduct(productId).pipe(
            map((res) => ({
                id: res.id,
                lineName: res.lineName ? `${res.lineName}` : '',
                lineId: res.lineId || 0,
                name: res.name,
                description: res.description ?? '',
                vnptManPn: res.vnptManPn,
                hardwareVersion: res.hardwareVersion ?? '',
                firmwareVersion: res.firmwareVersion ?? '',
                tradeName: res.tradeName,
                tradeCode: res.tradeCode,
                modelName: res.modelName || '',
                generation: res.generation || '',
                imageUrl: res.imageUrl || '',
                imageName: res.imageName || '',
                stage: LIFECYCLE_STAGE_PRODUCT_MAP[res.lifecycleStage] || '',
                productVersions: res.productVersions,
            })),
        );
    }

    onSearch(formValue: any, type: number): void {
        if (this.isSearching) {
            return;
        }
        this.isSearching = true;
        const state = window.history.state;
        //   TH nhân bản bên ngoài danh sách
        if (state && state?.version) {
            this.shouldUseVersionRef = state?.version && this.mode === 'create';
            this.versionRef = this.shouldUseVersionRef ? state.version.id : undefined;
        }
        //TH nhân bản bên trông màn thêm mới có cùng VNPT
        else {
            if (formValue.selectedProduct.id === this.currentProduct.id) {
                this.versionRef = formValue.selectedVersion;
            } else {
                this.versionRef = undefined;
            }
        }

        const versionId = formValue.selectedVersion;
        // 1 hồ sơ thiết kê , 2 hồ sơ sản xuất, 4 hồ sơ chất lượng , 7 cả ba
        const types = type;
    }

    onProductSelect(event: FilterChangeEvent) {
        const prod: ProductDetail = event.value;

        // Emit mảng mới cho dropdown
    }

    onSave(): void {
        // Cách 1: Gọi getData() cụ thể
        const processDataValidate = this.tabView.invokeMethod('process', 'validateProcess');
        let processData;
        if (processDataValidate) {
            processData = this.tabView.invokeMethod('process', 'getData');
        } else {
            this.tabView.invokeMethod('process', 'errorProcess');
        }
        const routingData = this.tabView.invokeMethod('routing', 'getData');
        console.log('Process:', processData);
        console.log('Routing:', routingData);

        // Cách 2: Gọi tất cả getData()
        const allData = this.tabView.getAllData();
        console.log('All:', allData);
    }

    handleClose() {
        this.router.navigate(['/pms/product-file'], {
            replaceUrl: true, // tránh tạo thêm history entry
        });
    }

    handleCancel() {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn Hủy',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.router.navigate(['/pms/product-file'], {
                    replaceUrl: true, // tránh tạo thêm history entry
                });
            },
        });
    }

    handleDeleteVersion(v: ProductRecordVersion): void {
        this.confirmationService.confirm({
            key: 'app-confirm',
            header: 'Xác nhận',
            message: 'Bạn có chắc chắn muốn xóa',
            icon: 'pi pi-exclamation-triangle',
            rejectLabel: 'Hủy',
            acceptLabel: 'Xóa',
            acceptIcon: 'pi pi-check mr-2',
            rejectIcon: 'pi pi-times mr-2',
            rejectButtonStyleClass: 'p-button-sm',
            acceptButtonStyleClass: 'p-button-outlined p-button-sm',
            accept: () => {
                this.productFileService.deleteVersion(this.version.id).subscribe({
                    next: () => {
                        this.alertService.success('Thành công', 'Xóa thành công');
                        this.router.navigate(['/pms/product-file'], {
                            replaceUrl: true, // tránh tạo thêm history entry
                        });
                    },
                    error: (err) => {
                        this.alertService.error('Lỗi', 'Xóa thất bại');
                        console.error('Delete error:', err);
                    },
                });
            },
        });
    }
}
