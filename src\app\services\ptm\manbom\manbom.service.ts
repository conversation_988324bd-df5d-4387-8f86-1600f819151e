import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, Observable, throwError } from 'rxjs';
import { ManBom, ManBomDetail } from 'src/app/models/interface/ptm/manbom';

@Injectable({ providedIn: 'root' })
export class ManBomService {
    path = '/pr/api/production-instruction';
    #http = inject(HttpClient);

    constructor(private http: HttpClient) {}

    getManBom(id: number): Observable<ManBom[]> {
        const url = `${this.path}/${id}/manbom`;
        return this.#http.get<ManBom[]>(url);
    }

    create(formData: any, instructionId: number): Observable<any> {
        const url = `${this.path}/${instructionId}/manbom`;
        return this.http.post(url, formData).pipe(catchError(this.handleError));
    }

    exportManBom(id: number) {
        const url = `${this.path}/${id}/manbom/export`;
        return this.#http.get(url, { responseType: 'text' }).pipe(catchError(this.handleError));
    }

    previewManBom(formData: any, id: number) {
        const url = `/pr/api/approval/production-instruction/${id}`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    comfirmManBom(formData: any, id: number) {
        const url = `/pr/api/approval/production-instruction/${id}/confirm`;
        return this.#http.post(url, formData).pipe(catchError(this.handleError));
    }

    /** Xử lý chung lỗi HTTP */
    private handleError(error: HttpErrorResponse) {
        // có thể tuỳ chỉnh thông báo, logging, …
        console.error('API error', error);
        return throwError(() => error);
    }
}
