import { BaseEntity } from '../../BaseEntity';

export interface Bor extends BaseEntity {
    reviewerIds: number[];
    listMan: ManDetail[];
    listLine: LineDetail[];
    listMaterial: MaterialDetail[];
}

export interface ManDetail extends BaseEntity {
    id: number;
    manBomId: number;
    instructionId: number;
    pfmeaId: number;
    workStandardId: number;
    description: number;
    unit: string;
    section: number;
    quantity: string;
    reference: string;
    materialType: number;
    consumptionRate: string;
    note: string;
    action: number;
}

export interface LineDetail extends BaseEntity {
    id: number;
    manBomId: number;
    instructionId: number;
    pfmeaId: number;
    workStandardId: number;
    description: number;
    unit: string;
    section: number;
    quantity: string;
    reference: string;
    materialType: number;
    consumptionRate: string;
    note: string;
    action: number;
}

export interface MaterialDetail extends BaseEntity {
    id: number;
    manBomId: number;
    instructionId: number;
    pfmeaId: number;
    workStandardId: number;
    description: number;
    unit: string;
    section: number;
    quantity: string;
    reference: string;
    materialType: number;
    consumptionRate: string;
    note: string;
    action: number;
}
