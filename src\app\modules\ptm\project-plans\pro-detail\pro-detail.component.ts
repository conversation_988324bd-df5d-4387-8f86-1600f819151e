import { Component, ViewChild, Input, AfterViewInit, OnInit, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { PanelModule } from 'primeng/panel';
import { FormsModule, FormArray, AbstractControl } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { FormGroup, FormControl, Validators, ReactiveFormsModule } from '@angular/forms';
import { FormCustomModule } from 'src/app/shared/form-module/form.custom.module';
import { Menu, MenuModule } from 'primeng/menu';
import { ProjectPlanService } from 'src/app/services/ptm/project-plan/project-plan.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ComboboxNonRSQLComponent } from 'src/app/shared/components/combobox-nonRSQL/combobox-nonRSQL.component';

import { MenuItem } from 'primeng/api';
import { CommonModule } from '@angular/common';
import { Task, IAssignee, TaskStatus } from 'src/app/models/interface/ptm';
import { ActionForTab, ProjectWorkingSetting, ProjectWorkingSettingRequest, SettingType } from 'src/app/models/interface/ptm/project-plan';
import { PopupComponent } from 'src/app/shared/components/popup/popup.component';
@Component({
    selector: 'app-pro-detail',
    templateUrl: './pro-detail.component.html',
    styleUrls: ['./pro-detail.component.scss'],
    standalone: true,
    imports: [
        PanelModule,
        PopupComponent,
        ButtonModule,
        TableModule,
        FormCustomModule,
        DropdownModule,
        CalendarModule,
        FormsModule,
        InputTextModule,
        CheckboxModule,
        MenuModule,
        CommonModule,
        ComboboxNonRSQLComponent,
        ReactiveFormsModule,
    ],
})
export class ProDetailComponent implements OnInit {
    @Input() formArray!: FormArray;
    @Input() projectId: number;
    @Output() workingDayUpdated = new EventEmitter<void>();
    settingsForm: FormArray;
    @ViewChild('WorkingDayPopup', { static: true }) private workingDayPopup!: PopupComponent;
    @ViewChild('menu') menu: Menu;
    @ViewChild('userFilter', { static: false })
    userFilter!: ComboboxNonRSQLComponent;
    // formGroup: FormGroup;
    tasks: Task[] = [];
    selectedDates: Date[] = [];
    disabledDays: number[] = [0, 6];
    rangeDates: null;
    firstTimeFlags = new WeakMap();

    statusOptions = [
        { label: 'Đã hoàn thành', value: TaskStatus.COMPLETED },
        { label: 'Chưa hoàn thành', value: TaskStatus.NOT_COMPLETED },
        { label: 'Đang chờ xử lý', value: TaskStatus.PENDING },
        { label: 'Đã hủy', value: TaskStatus.CANCELLED },
    ];

    menuItems: MenuItem[] = [];
    dayTypes = [
        { label: 'Working', value: 1 },
        { label: 'Nonworking', value: 2 },
    ];

    constructor(
        private pps: ProjectPlanService,
        private alertService: AlertService,
    ) {}

    ngOnInit() {
        this.settingsForm = new FormArray([]);
        this.addSettingRow();
    }
    ngAfterViewInit(): void {
        setTimeout(() => {
            if (this.formArray?.length > 0) {
                this.formArray.controls.forEach((control) => {
                    const formGroup = control as FormGroup;
                    this.setupTaskChangeListener(formGroup);
                });
            }
        });
    }
    allowOnlyPredecessorChars(event: KeyboardEvent): void {
        const allowed = /[0-9.,]/;
        const key = event.key;

        if (!allowed.test(key)) {
            event.preventDefault(); // chặn ký tự không hợp lệ
        }
    }
    onDateRangeToggle(formGroup: FormGroup): void {
        const enableRange = formGroup.get('enableDateRange')?.value;
        const enableSpecific = formGroup.get('enableSpecificDate')?.value;

        if (enableRange && !formGroup.get('rangeDates')?.value) {
            formGroup.get('rangeDates')?.setErrors({ required: true });
        }

        if (enableSpecific && !formGroup.get('specificDate')?.value) {
            formGroup.get('specificDate')?.setErrors({ required: true });
        }
    }
    validateProgress(rowIndex: number): void {
        const formGroup = this.formArray.at(rowIndex) as FormGroup;
        const raw = String(formGroup.get('progress')?.value || '').replace(',', '.');
        const value = parseFloat(raw);

        if (isNaN(value)) {
            formGroup.get('progress')?.setValue(null);
            return;
        }

        const finalValue = Math.min(value, 100); // luôn giới hạn 100
        const predecessorStr = formGroup.get('predecessor')?.value;

        // ⛔ CHỈ kiểm tra khi nhập đúng 100%
        if (finalValue === 100 && predecessorStr) {
            const predecessors = predecessorStr.split(',').map((s: string) => s.trim());
            const invalid = predecessors.some((stt) => {
                const match = this.formArray.controls.find((ctrl) => ctrl.get('displaySTT')?.value === stt);
                return match?.get('status')?.value !== TaskStatus.COMPLETED;
            });

            if (invalid) {
                // this.alertService.warning('Không thể đặt tiến độ 100% khi task tiền nhiệm chưa hoàn thành');
                formGroup.get('progress')?.setValue(null); // reset về rỗng
                return;
            }
        }

        // ✅ Cho phép nhập bất kỳ giá trị < 100 hoặc đã hợp lệ
        formGroup.get('progress')?.setValue(finalValue);

        // ⏫ Nếu giá trị đạt 100% thì cập nhật status = Completed
        if (finalValue === 100 && formGroup.get('status')?.value !== TaskStatus.COMPLETED) {
            formGroup.get('status')?.setValue(TaskStatus.COMPLETED);
        }
    }
    onStatusChange(rowIndex: number): void {
        const formGroup = this.formArray.at(rowIndex) as FormGroup;
        const status = formGroup.get('status')?.value;
        const predecessorStr = formGroup.get('predecessor')?.value;

        if (status === TaskStatus.COMPLETED) {
            if (predecessorStr) {
                const predecessors = predecessorStr.split(',').map((s) => s.trim());
                const invalid = predecessors.some((stt) => {
                    const match = this.formArray.controls.find((ctrl) => ctrl.get('displaySTT')?.value === stt);
                    return match?.get('status')?.value !== TaskStatus.COMPLETED;
                });

                if (invalid) {
                    this.alertService.warning('Không thể chọn "Đã hoàn thành" khi task tiền nhiệm chưa hoàn thành');
                    formGroup.get('status')?.setValue(TaskStatus.NOT_COMPLETED);
                    return;
                }
            }

            // ✅ Nếu task chính → cập nhật subtask
            const stt = formGroup.get('displaySTT')?.value;
            const level = formGroup.get('level')?.value;
            if (stt && level === 1) {
                this.formArray.controls.forEach((ctrl) => {
                    const childStt = ctrl.get('displaySTT')?.value;
                    const childLevel = ctrl.get('level')?.value;
                    if (childStt?.startsWith(stt + '.') && childLevel > 1) {
                        ctrl.get('status')?.setValue(TaskStatus.COMPLETED);
                    }
                });
            }

            // ✅ Autofill progress nếu chưa có
            if (formGroup.get('progress')?.value !== 100) {
                formGroup.get('progress')?.setValue(100);
            }
        }

        // ✅ Nếu bỏ chọn “Đã hoàn thành” mà progress là 100 → reset
        if (status !== TaskStatus.COMPLETED && formGroup.get('progress')?.value === 100) {
            formGroup.get('progress')?.setValue(null);
        }
    }

    isProgressReadOnly(rowIndex: number): boolean {
        const fg = this.formArray.at(rowIndex) as FormGroup;
        const predecessor = fg.get('predecessor')?.value;
        if (!predecessor) return false;

        const predecessors = predecessor.split(',').map((s: string) => s.trim());
        return predecessors.some((stt) => {
            const match = this.formArray.controls.find((ctrl) => ctrl.get('displaySTT')?.value === stt);
            return match?.get('status')?.value !== TaskStatus.COMPLETED;
        });
    }
    setupTaskChangeListener(formGroup: FormGroup): void {
        const editableFields = ['name', 'timeline', 'duration', 'predecessor', 'assignee', 'progress', 'status', 'note'];

        editableFields.forEach((field) => {
            formGroup.get(field)?.valueChanges.subscribe(() => {
                const raw = formGroup.get('rawData')?.value;
                if (!raw) return;

                const previous = JSON.parse(raw);
                const current = {
                    name: formGroup.get('name')?.value,
                    timeline: formGroup.get('timeline')?.value,
                    duration: formGroup.get('duration')?.value,
                    predecessor: formGroup.get('predecessor')?.value,
                    assignee: formGroup.get('assignee')?.value,
                    progress: formGroup.get('progress')?.value,
                    status: formGroup.get('status')?.value,
                    note: formGroup.get('note')?.value,
                };

                const changed = JSON.stringify(current) !== JSON.stringify(previous);
                const currentAction = formGroup.get('action')?.value;

                if (changed && currentAction !== ActionForTab.CREATE && currentAction !== ActionForTab.DELETE) {
                    formGroup.get('action')?.setValue(ActionForTab.UPDATE);
                }
            });
        });
    }

    validatePredecessor(): void {
        // Lấy danh sách STT của các task main (level === 1)
        const mainTaskSTTs = this.formArray.controls
            .filter((ctrl) => (ctrl.get('level')?.value ?? 1) === 1)
            .map((ctrl) => ctrl.get('displaySTT')?.value)
            .filter(Boolean);

        this.formArray.controls.forEach((ctrl: AbstractControl) => {
            const fg = ctrl as FormGroup;
            const predValue = fg.get('predecessor')?.value;

            if (!predValue || predValue.trim() === '') {
                fg.get('predecessor')?.setErrors(null); // không bắt buộc
                return;
            }

            const parts = predValue.split(',').map((p) => p.trim());
            const isValid = parts.every((p) => /^[1-9]\d*$/.test(p) && mainTaskSTTs.includes(p));

            if (!isValid) {
                fg.get('predecessor')?.setErrors({ invalidSTT: true });
            } else {
                fg.get('predecessor')?.setErrors(null);
            }
        });
    }

    addTask() {
        const newTask = new FormGroup({
            name: new FormControl('', Validators.required),
            timeline: new FormControl(null, Validators.required),
            duration: new FormControl(null),
            predecessor: new FormControl(''),
            assignee: new FormControl(null),
            progress: new FormControl(null),
            status: new FormControl(TaskStatus.NOT_COMPLETED),
            note: new FormControl(''),
            level: new FormControl(1),
            displaySTT: new FormControl(''),
            action: new FormControl(ActionForTab.CREATE),
        });
        this.setupTaskChangeListener(newTask);
        this.formArray.push(newTask);
        this.rebuildSTT(); // cập nhật lại displaySTT
    }

    addNewTask() {
        const newTask: Task = {
            name: '',
            timeLine: { start: null, end: null },
            duration: 0,
            assignee: { id: '', name: 'Chọn người phụ trách' },
            progress: 0,
            status: TaskStatus.NOT_COMPLETED,
        };
        this.tasks.push(newTask);
    }
    addSettingRow() {
        const settingGroup = new FormGroup({
            content: new FormControl('', Validators.required),
            dayType: new FormControl(null, Validators.required),
            rangeDates: new FormControl(null),
            specificDate: new FormControl(null),
            enableDateRange: new FormControl(false),
            enableSpecificDate: new FormControl(false),
        });

        this.settingsForm.push(settingGroup);
    }
    onApply() {
        // this.settingsForm.markAllAsTouched();
        if (this.settingsForm.valid) {
            this.saveWorkingSettings();
        } else {
            // this.alertService.warning('Vui lòng điền đầy đủ thông tin bắt buộc');
            this.markFormGroupTouched(this.settingsForm);
        }
    }

    private markFormGroupTouched(formGroup: FormArray) {
        formGroup.controls.forEach((control) => {
            if (control instanceof FormGroup) {
                Object.values(control.controls).forEach((c) => c.markAsTouched());
            }
        });
    }
    saveWorkingSettings() {
        const settingsPayload: ProjectWorkingSetting[] = this.settingsForm.controls.map((control) => {
            const formGroup = control as FormGroup;
            const value = formGroup.value;

            const rangeDates = Array.isArray(value.rangeDates) ? value.rangeDates : [];
            const dateFrom = rangeDates[0] ? new Date(rangeDates[0]).getTime() : null;
            const dateTo = rangeDates[1] ? new Date(rangeDates[1]).getTime() : null;

            const specificDateArr: number[] = [];

            if (value.specificDate instanceof Date) {
                specificDateArr.push(new Date(value.specificDate).getTime());
            } else if (Array.isArray(value.specificDate)) {
                value.specificDate.forEach((d: Date) => {
                    if (d instanceof Date) {
                        specificDateArr.push(new Date(d).getTime());
                    }
                });
            }
            const settingType =
                value.enableDateRange && value.enableSpecificDate
                    ? SettingType.ALL_TYPE_DAY
                    : value.enableDateRange
                      ? SettingType.RANGE
                      : value.enableSpecificDate
                        ? SettingType.SPECIFIC_DAY
                        : null;

            return {
                id: value.id || 0,
                projectId: this.projectId,
                settingType,
                dayType: value.dayType?.value === 'working' ? 1 : 2,
                content: value.content || '',
                dateFrom,
                dateTo,
                specificDate: specificDateArr.join(','),
            };
        });

        const requestBody: ProjectWorkingSettingRequest = { settings: settingsPayload };

        this.pps.updateProjectWorkingSettings(this.projectId, requestBody).subscribe({
            next: () => {
                this.alertService.success('Cập nhật cấu hình làm việc thành công');
                this.workingDayPopup.closeDialog();
                this.workingDayUpdated.emit();
            },
            // error: (error) => {
            //     this.alertService.error('Có lỗi xảy ra khi cập nhật cấu hình làm việc');
            //     console.error('Lỗi khi cập nhật cài đặt làm việc:', error);
            // },
        });
    }

    getRowClassBySTT(control: any): string {
        console.log('pppp2', control);
        if (!control || typeof control.get !== 'function') {
            console.log('pppp4');
            return '';
        }
        console.log('pppp3');

        const stt = control.get('displaySTT')?.value;

        const levelDepth = stt?.split('.').length || 0;
        console.log('pppp', levelDepth);
        switch (levelDepth) {
            case 1:
                return 'task-level-1';
            case 2:
                return 'task-level-2';
            // case 3:
            //     return 'task-level-3';
            default:
                return '';
        }
    }

    onDateRangeSelect(rowIndex: number, calendarRef: any): void {
        const control = this.formArray.at(rowIndex)?.get('timeline');
        const value = control?.value;

        if (Array.isArray(value) && value[0] && value[1]) {
            // PrimeNG không tự đóng nếu dùng formControl ⇒ giả lập click ngoài để đóng
            setTimeout(() => {
                const event = new MouseEvent('mousedown', { bubbles: true });
                document.body.dispatchEvent(event);
            }, 100); // đợi 1 chút để không bị conflict với UI cập nhật
        }
    }

    onMenuClick(event: MouseEvent, rowIndex: number, menu: Menu) {
        this.menuItems = [
            {
                label: 'Thêm subtask',
                icon: 'pi pi-plus',
                command: () => this.addSubTask(rowIndex),
            },
            {
                label: 'Xóa',
                icon: 'pi pi-trash',
                command: () => this.removeTask(rowIndex),
            },
        ];
        menu.toggle(event);
    }

    addSubTask(parentIndex: number): void {
        const parentForm = this.formArray;
        const parentControl = parentForm.at(parentIndex) as FormGroup;
        const parentSTT = parentControl.get('displaySTT')?.value;
        const parentLevel = parentControl.get('level')?.value || 1;
        const newLevel = parentLevel + 1;

        let insertIndex = parentIndex + 1;
        let maxSubIndex = 0;

        // Tìm vị trí để chèn subtask và xác định subIndex
        for (let i = insertIndex; i < parentForm.length; i++) {
            const ctrl = parentForm.at(i) as FormGroup;
            const lvl = ctrl.get('level')?.value || 1;
            const stt = ctrl.get('displaySTT')?.value || '';

            if (lvl <= parentLevel) {
                break;
            }

            // Nếu subtask hiện có là con của parent
            if (stt.startsWith(parentSTT + '.')) {
                const match = stt.match(new RegExp(`^${parentSTT}\\.(\\d+)$`));
                if (match) {
                    const num = parseInt(match[1], 10);
                    if (!isNaN(num)) {
                        maxSubIndex = Math.max(maxSubIndex, num);
                    }
                }
            }

            insertIndex = i + 1;
        }

        const newSTT = `${parentSTT}.${maxSubIndex + 1}`;

        const newSubtask = new FormGroup({
            name: new FormControl(''),
            timeline: new FormControl(null),
            duration: new FormControl(null),
            predecessor: new FormControl(''),
            assignee: new FormControl(null),
            progress: new FormControl(null),
            status: new FormControl(TaskStatus.NOT_COMPLETED),
            note: new FormControl(''),
            level: new FormControl(newLevel),
            displaySTT: new FormControl(newSTT),
            action: new FormControl(ActionForTab.CREATE),
        });
        this.setupTaskChangeListener(newSubtask);
        parentForm.insert(insertIndex, newSubtask);
    }

    rebuildSTT(): void {
        const levelCounters: number[] = [];

        for (let i = 0; i < this.formArray.length; i++) {
            const task = this.formArray.at(i);
            const level: number = task.get('level')?.value || 1;

            // Cắt bớt các cấp sâu hơn hiện tại
            levelCounters.length = level;

            // Tăng bộ đếm cho cấp hiện tại
            if (levelCounters[level - 1] == null) {
                levelCounters[level - 1] = 1;
            } else {
                levelCounters[level - 1]++;
            }

            // Tạo displaySTT dạng "1.2.1"
            const displaySTT = levelCounters.slice(0, level).join('.');
            task.get('displaySTT')?.setValue(displaySTT);
        }
    }
    get visibleTasks() {
        return this.formArray.controls.filter((c) => c.get('action')?.value !== ActionForTab.DELETE);
    }

    removeTask(index: number): void {
        const task = this.formArray.at(index) as FormGroup;
        const actionControl = task.get('action');

        const isNewTask = actionControl?.value === ActionForTab.CREATE || !task.get('id')?.value;

        if (isNewTask) {
            this.formArray.removeAt(index); // task/subtask mới thì xóa khỏi form luôn
        } else {
            actionControl?.setValue(ActionForTab.DELETE); // task cũ thì gắn cờ xóa
        }

        this.rebuildSTT();
    }

    highlightWeekends(event: any) {
        if (event.day === 0 || event.day === 6) {
            event.styleClass = 'weekend';
        }
    }

    handlePanelShow(ref: any) {
        if (!this.firstTimeFlags.has(ref)) {
            this.firstTimeFlags.set(ref, true);
            return;
        }
        ref.fetchOptions(null);
    }

    openWorkingDay() {
        this.workingDayPopup.openDialog();
        this.loadWorkingSettings();
    }
    loadWorkingSettings(): void {
        if (!this.projectId) return;

        this.pps.getProjectWorkingSettings(this.projectId).subscribe((response: any[]) => {
            this.settingsForm.clear(); // reset form cũ

            response.forEach((item) => {
                // const matchedDayType = this.dayTypes.find((d) => d.value === item.dayType);
                const settingGroup = new FormGroup({
                    id: new FormControl(item.id),
                    content: new FormControl(item.content || '', Validators.required),
                    dayType: new FormControl(item.dayType, Validators.required),
                    rangeDates: new FormControl(item.dateFrom && item.dateTo ? [new Date(item.dateFrom), new Date(item.dateTo)] : null),
                    specificDate: new FormControl(item.specificDate ? new Date(Number(item.specificDate.split(',')[0])) : null),
                    enableDateRange: new FormControl(!!item.dateFrom && !!item.dateTo),
                    enableSpecificDate: new FormControl(!!item.specificDate),
                });

                this.settingsForm.push(settingGroup);
            });
        });
    }
}
