// src/app/modules/pms/shared/services/product-software.service.ts
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ProductSoftware, ProfileSoftware, UpdateVersionDto } from 'src/app/models/interface/pms';
import { ParamsTable } from 'src/app/shared/table-module/table.common.service';

@Injectable({ providedIn: 'root' })
export class TechnologyTransferService {
    path = '/pr/api/transfer-request';
    #http = inject(HttpClient);

    /** Phân trang + lọc nâng cao */

    /** Lấy toàn bộ software (không phân trang) */
    getPage(
        { native = '', pageable = '' }: ParamsTable,
        /* nếu cần body filter bạn có thể thêm param body?: Record<string,any> */
    ): Observable<HttpResponse<ProductSoftware[]>> {
        // Nếu bảng build ra "&page=1&size=2", ta chuyển thành HttpParams
        const combined = `${native}${pageable}`;
        const raw = combined.startsWith('&') ? combined.substring(1) : combined;
        const params = new HttpParams({ fromString: raw });

        return this.#http.get<ProductSoftware[]>(this.path + '/filter', {
            params,
            observe: 'response', // để TableCommonService còn đọc header X-Total-Count
        });
    }

    /** Lấy chi tiết một software theo ID */
    // getOne(id: number): Observable<ProductSoftware> {
    //     return this.#http.get<ProductSoftware>(`/pr/api/product-softwares/${id}`).pipe(catchError(this.handleError));
    // }

    /** Tạo mới software */
    create(payload: ProductSoftware): Observable<ProductSoftware> {
        return this.#http.post<ProductSoftware>(this.path, payload);
    }

    /** Cập nhật software */
    update(id: number, payload: ProductSoftware): Observable<ProductSoftware> {
        return this.#http.put<ProductSoftware>(this.path + `/${id}`, payload);
    }
    getSoftwareVersionById(id: number): Observable<ProductSoftware> {
        return this.#http.get<ProductSoftware>(this.path + `/${id}`);
    }
    getSoftwareResource(id: number): Observable<ProductSoftware> {
        return this.#http.get<ProductSoftware>(this.path + `/${id}/latest-version`);
    }
    // lịch sử thay đổi của một version
    getVersionsByRootId(id: number): Observable<ProductSoftware[]> {
        return this.#http.get<ProductSoftware[]>(this.path + `/${id}/history`);
    }
    getVersions(id: number): Observable<ProductSoftware[]> {
        return this.#http.get<ProductSoftware[]>(this.path + `/${id}/versions`);
    }
    getProfiles(id: number): Observable<ProfileSoftware[]> {
        return this.#http.get<ProfileSoftware[]>(this.path + `/${id}/documents`);
    }
    /** Xóa software theo ID */
    delete(id: number): Observable<void> {
        return this.#http.delete<void>(this.path + `/${id}`);
    }

    private buildCustomQueryString(nativePart: string, pageablePart: string): string {
        // Bỏ dấu '&' đầu (nếu có)
        const rawNative = nativePart ? nativePart.replace(/^&/, '') : '';
        const rawPageable = pageablePart ? pageablePart.replace(/^&/, '') : '';

        // Tách rawNative thành từng cặp "key=value"
        const nativePairs = rawNative ? rawNative.split('&') : [];
        const multiParts: string[] = [];

        nativePairs.forEach((pair) => {
            const [key, value] = pair.split('=');
            if (key === 'productIds' && value) {
                // Nếu value = "2,3" → phân tách thành ["2","3"]
                value.split(',').forEach((id) => {
                    if (id) {
                        multiParts.push(`productIds=${encodeURIComponent(id)}`);
                    }
                });
            } else if (pair) {
                // Giữ nguyên nếu key khác
                multiParts.push(pair);
            }
        });

        // Tách pageable thành ["page=0","size=10", ...]
        const pageablePairs = rawPageable ? rawPageable.split('&') : [];

        // Ghép pageable trước, rồi native đã tách
        const allParts = [...pageablePairs, ...multiParts];
        return allParts.join('&');
    }

    /**
     * Hàm được TableCommonService gọi thay vì productSoftwareService.getPage
     * @param params.native ví dụ "&productIds=2,3"
     * @param params.pageable ví dụ "&page=0&size=10"
     */
    getPageOverwrite(params: ParamsTable,) {
        const customQs = this.buildCustomQueryString(params.native, params.pageable);
        const subQuery = [];
        // Nếu customQs rỗng, thì URL là "/.../filter?".strip("&")
        const queryString = customQs.replace(/^&/, '');

        let query = '';
        if (subQuery.length) {
            query = `&${subQuery.join('&')}`;
        }

        return this.#http.post<ProductSoftware[]>(this.path + `/search?${queryString}${query}`, {
            observe: 'response',
        });
    }
    getApprovedDocuments(id: number): Observable<ProfileSoftware[]> {
        return this.#http.get<ProfileSoftware[]>(this.path + `/${id}/approved-documents`);
    }
    changeVersion(payload: UpdateVersionDto[]): Observable<UpdateVersionDto[]> {
        return this.#http.post<UpdateVersionDto[]>(`/pr/api/product-version/duplicate/new-software`, payload);
    }
}
