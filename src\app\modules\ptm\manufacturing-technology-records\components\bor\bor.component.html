<ng-container>
    <div class="p-fluid tw-space-y-2">
        <app-info-share [title]="title"></app-info-share>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[15px] tw-pb-[10px] border-bottom">
        <label class="tw-font-semibold tw-text-base label">Chi tiết BOR</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Track Changes" class="p-button-sm p-button-primary tw-h-10 tw-whitespace-nowrap"></button>
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[15px] tw-pb-[10px] border-bottom">
        <label class="tw-font-semibold tw-text-base label"><PERSON>ảng định mức MAN</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>

        <div #contaninerListMan>
            <app-form-item label="" [disableAutoErrorMessage]="true">
                <app-form #form [formGroup]="formGroup" layout="vertical">
                    <div formArrayName="listMan">
                        <p-panel [toggleable]="true">
                            <ng-template pTemplate="icons">
                                <button
                                    pButton
                                    type="button"
                                    class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                                    appFullscreenToggle
                                    [target]="contaninerListMan"
                                ></button>
                            </ng-template>
                            <p-table styleClass="p-datatable-gridlines" [value]="listMan.controls">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th style="min-width: 9rem">Level</th>
                                        <th style="min-width: 9rem">Công đoạn <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Next OC</th>
                                        <th style="min-width: 9rem">Đặc tính công việc (Symbol) <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Operation Description (OD) <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Product and process characteristics <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Online</th>
                                        <th style="min-width: 9rem">Offline</th>
                                        <th style="max-width: 5rem">Thao tác</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                    <tr [formGroupName]="rowIndex" *ngIf="item.get('action')?.value !== 3">
                                        <td>
                                            <app-form-item [disableAutoErrorMessage]="true" label=""> {{ rowIndex + 1 }}</app-form-item>
                                        </td>

                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="4" formControlName="oc" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="1000" formControlName="nextOc" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="symbolType"
                                                    formControlName="symbol"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                    (onBlur)="handleDropdownBlur('symbol', rowIndex)"
                                                    (onChange)="handleDropdownChange('symbol', $event.value, rowIndex)"
                                                />
                                                <div
                                                    *ngIf="
                                                        item.get('symbol')?.invalid &&
                                                        (dropdownTouched[rowIndex]?.symbol || formSubmitted) &&
                                                        item.get('symbol')?.touched &&
                                                        item.get('symbol')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="100" formControlName="operationDescription" />
                                                <div
                                                    *ngIf="
                                                        item.get('operationDescription')?.invalid &&
                                                        item.get('operationDescription')?.touched &&
                                                        item.get('operationDescription')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="productProcessType"
                                                    formControlName="productAndProcessCharacteristics"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                    (onBlur)="handleDropdownBlur('characteristics', rowIndex)"
                                                    (onChange)="handleDropdownChange('characteristics', $event.value, rowIndex)"
                                                />
                                                <div
                                                    *ngIf="
                                                        item.get('productAndProcessCharacteristics')?.invalid &&
                                                        (dropdownTouched[rowIndex]?.characteristics || formSubmitted) &&
                                                        item.get('productAndProcessCharacteristics')?.touched &&
                                                        item.get('productAndProcessCharacteristics')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-checkbox
                                                    name="online"
                                                    formControlName="online"
                                                    [binary]="true"
                                                    (onChange)="item.updateValueAndValidity()"
                                                ></p-checkbox>
                                                <div
                                                    *ngIf="
                                                        !item.get('online')?.value &&
                                                        !item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Phải chọn một trong hai trường: Online hoặc Offline</span>
                                                </div>
                                                <div
                                                    *ngIf="
                                                        item.get('online')?.value &&
                                                        item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Không thể chọn cả hai trường: Online và Offline</span>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-checkbox
                                                    name="offline"
                                                    formControlName="offline"
                                                    [binary]="true"
                                                    (onChange)="item.updateValueAndValidity()"
                                                ></p-checkbox>
                                                <div
                                                    *ngIf="
                                                        !item.get('online')?.value &&
                                                        !item.get('offline')?.value &&
                                                        (item.get('offline')?.dirty || item.get('offline')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Phải chọn một trong hai trường: Online hoặc Offline</span>
                                                </div>
                                                <div
                                                    *ngIf="
                                                        item.get('online')?.value &&
                                                        item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Không thể chọn cả hai trường: Online và Offline</span>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <button
                                                    [disabled]="mode === 'view'"
                                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                                    pTooltip="Hủy"
                                                    tooltipPosition="top"
                                                    type="button"
                                                    (click)="removeItem(rowIndex)"
                                                >
                                                    <span class="pi pi-times"></span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                            <div class="tw-mt-3">
                                <p-button
                                    [disabled]="mode === 'view'"
                                    label="Thêm dòng"
                                    icon="pi pi-plus"
                                    severity="info"
                                    size="small"
                                    (click)="addItem()"
                                ></p-button>
                            </div>
                        </p-panel>
                    </div>
                </app-form>
            </app-form-item>
        </div>
    </div>

    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[15px] tw-pb-[10px] border-bottom">
        <label class="tw-font-semibold tw-text-base label">Bảng định mức LINE, MACHINE</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>

        <div #contaninerListLine>
            <app-form-item label="" [disableAutoErrorMessage]="true">
                <app-form #form [formGroup]="formGroup" layout="vertical">
                    <div formArrayName="listLine">
                        <p-panel [toggleable]="true">
                            <ng-template pTemplate="icons">
                                <button
                                    pButton
                                    type="button"
                                    class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                                    appFullscreenToggle
                                    [target]="contaninerListLine"
                                ></button>
                            </ng-template>
                            <p-table styleClass="p-datatable-gridlines" [value]="listLine.controls">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th style="min-width: 9rem">Level</th>
                                        <th style="min-width: 9rem">Công đoạn <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Next OC</th>
                                        <th style="min-width: 9rem">Đặc tính công việc (Symbol) <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Operation Description (OD) <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Product and process characteristics <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Online</th>
                                        <th style="min-width: 9rem">Offline</th>
                                        <th style="max-width: 5rem">Thao tác</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                    <tr [formGroupName]="rowIndex" *ngIf="item.get('action')?.value !== 3">
                                        <td>
                                            <app-form-item [disableAutoErrorMessage]="true" label=""> {{ rowIndex + 1 }}</app-form-item>
                                        </td>

                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="4" formControlName="oc" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="1000" formControlName="nextOc" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="symbolType"
                                                    formControlName="symbol"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                    (onBlur)="handleDropdownBlur('symbol', rowIndex)"
                                                    (onChange)="handleDropdownChange('symbol', $event.value, rowIndex)"
                                                />
                                                <div
                                                    *ngIf="
                                                        item.get('symbol')?.invalid &&
                                                        (dropdownTouched[rowIndex]?.symbol || formSubmitted) &&
                                                        item.get('symbol')?.touched &&
                                                        item.get('symbol')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="100" formControlName="operationDescription" />
                                                <div
                                                    *ngIf="
                                                        item.get('operationDescription')?.invalid &&
                                                        item.get('operationDescription')?.touched &&
                                                        item.get('operationDescription')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="productProcessType"
                                                    formControlName="productAndProcessCharacteristics"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                    (onBlur)="handleDropdownBlur('characteristics', rowIndex)"
                                                    (onChange)="handleDropdownChange('characteristics', $event.value, rowIndex)"
                                                />
                                                <div
                                                    *ngIf="
                                                        item.get('productAndProcessCharacteristics')?.invalid &&
                                                        (dropdownTouched[rowIndex]?.characteristics || formSubmitted) &&
                                                        item.get('productAndProcessCharacteristics')?.touched &&
                                                        item.get('productAndProcessCharacteristics')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-checkbox
                                                    name="online"
                                                    formControlName="online"
                                                    [binary]="true"
                                                    (onChange)="item.updateValueAndValidity()"
                                                ></p-checkbox>
                                                <div
                                                    *ngIf="
                                                        !item.get('online')?.value &&
                                                        !item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Phải chọn một trong hai trường: Online hoặc Offline</span>
                                                </div>
                                                <div
                                                    *ngIf="
                                                        item.get('online')?.value &&
                                                        item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Không thể chọn cả hai trường: Online và Offline</span>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-checkbox
                                                    name="offline"
                                                    formControlName="offline"
                                                    [binary]="true"
                                                    (onChange)="item.updateValueAndValidity()"
                                                ></p-checkbox>
                                                <div
                                                    *ngIf="
                                                        !item.get('online')?.value &&
                                                        !item.get('offline')?.value &&
                                                        (item.get('offline')?.dirty || item.get('offline')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Phải chọn một trong hai trường: Online hoặc Offline</span>
                                                </div>
                                                <div
                                                    *ngIf="
                                                        item.get('online')?.value &&
                                                        item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Không thể chọn cả hai trường: Online và Offline</span>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <button
                                                    [disabled]="mode === 'view'"
                                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                                    pTooltip="Hủy"
                                                    tooltipPosition="top"
                                                    type="button"
                                                    (click)="removeItem(rowIndex)"
                                                >
                                                    <span class="pi pi-times"></span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                            <div class="tw-mt-3">
                                <p-button
                                    [disabled]="mode === 'view'"
                                    label="Thêm dòng"
                                    icon="pi pi-plus"
                                    severity="info"
                                    size="small"
                                    (click)="addItem()"
                                ></p-button>
                            </div>
                        </p-panel>
                    </div>
                </app-form>
            </app-form-item>
        </div>
    </div>

    <div class="tw-flex tw-items-center tw-justify-between tw-gap-4 tw-flex-wrap tw-mb-[15px] tw-pb-[10px] border-bottom">
        <label class="tw-font-semibold tw-text-base label">Bảng định mức Material</label>

        <div class="tw-flex tw-gap-2">
            <button pButton type="button" label="Xuất excel" class="p-button-sm p-button-secondary tw-h-10 tw-whitespace-nowrap"></button>
        </div>

        <div #contaninerListMaterial>
            <app-form-item label="" [disableAutoErrorMessage]="true">
                <app-form #form [formGroup]="formGroup" layout="vertical">
                    <div formArrayName="listMaterial">
                        <p-panel [toggleable]="true">
                            <ng-template pTemplate="icons">
                                <button
                                    pButton
                                    type="button"
                                    class="p-button-outlined p-button-sm toggle-btn pi pi-arrows-alt"
                                    appFullscreenToggle
                                    [target]="contaninerListMaterial"
                                ></button>
                            </ng-template>
                            <p-table styleClass="p-datatable-gridlines" [value]="listMaterial.controls">
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th style="min-width: 9rem">Level</th>
                                        <th style="min-width: 9rem">Công đoạn <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Next OC</th>
                                        <th style="min-width: 9rem">Đặc tính công việc (Symbol) <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Operation Description (OD) <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Product and process characteristics <span class="tw-text-red-500">*</span></th>
                                        <th style="min-width: 9rem">Online</th>
                                        <th style="min-width: 9rem">Offline</th>
                                        <th style="max-width: 5rem">Thao tác</th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-item let-rowIndex="rowIndex">
                                    <tr [formGroupName]="rowIndex" *ngIf="item.get('action')?.value !== 3">
                                        <td>
                                            <app-form-item [disableAutoErrorMessage]="true" label=""> {{ rowIndex + 1 }}</app-form-item>
                                        </td>

                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="4" formControlName="oc" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="1000" formControlName="nextOc" />
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="symbolType"
                                                    formControlName="symbol"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                    (onBlur)="handleDropdownBlur('symbol', rowIndex)"
                                                    (onChange)="handleDropdownChange('symbol', $event.value, rowIndex)"
                                                />
                                                <div
                                                    *ngIf="
                                                        item.get('symbol')?.invalid &&
                                                        (dropdownTouched[rowIndex]?.symbol || formSubmitted) &&
                                                        item.get('symbol')?.touched &&
                                                        item.get('symbol')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <input type="text" class="tw-w-full" pInputText maxlength="100" formControlName="operationDescription" />
                                                <div
                                                    *ngIf="
                                                        item.get('operationDescription')?.invalid &&
                                                        item.get('operationDescription')?.touched &&
                                                        item.get('operationDescription')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-dropdown
                                                    [options]="productProcessType"
                                                    formControlName="productAndProcessCharacteristics"
                                                    optionLabel="label"
                                                    optionValue="value"
                                                    class="tw-w-full"
                                                    [showClear]="true"
                                                    appendTo="body"
                                                    (onBlur)="handleDropdownBlur('characteristics', rowIndex)"
                                                    (onChange)="handleDropdownChange('characteristics', $event.value, rowIndex)"
                                                />
                                                <div
                                                    *ngIf="
                                                        item.get('productAndProcessCharacteristics')?.invalid &&
                                                        (dropdownTouched[rowIndex]?.characteristics || formSubmitted) &&
                                                        item.get('productAndProcessCharacteristics')?.touched &&
                                                        item.get('productAndProcessCharacteristics')?.errors?.required
                                                    "
                                                    class="text-red-400"
                                                >
                                                    Trường này là bắt buộc
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-checkbox
                                                    name="online"
                                                    formControlName="online"
                                                    [binary]="true"
                                                    (onChange)="item.updateValueAndValidity()"
                                                ></p-checkbox>
                                                <div
                                                    *ngIf="
                                                        !item.get('online')?.value &&
                                                        !item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Phải chọn một trong hai trường: Online hoặc Offline</span>
                                                </div>
                                                <div
                                                    *ngIf="
                                                        item.get('online')?.value &&
                                                        item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Không thể chọn cả hai trường: Online và Offline</span>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <app-form-item label="" [disableAutoErrorMessage]="true">
                                                <p-checkbox
                                                    name="offline"
                                                    formControlName="offline"
                                                    [binary]="true"
                                                    (onChange)="item.updateValueAndValidity()"
                                                ></p-checkbox>
                                                <div
                                                    *ngIf="
                                                        !item.get('online')?.value &&
                                                        !item.get('offline')?.value &&
                                                        (item.get('offline')?.dirty || item.get('offline')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Phải chọn một trong hai trường: Online hoặc Offline</span>
                                                </div>
                                                <div
                                                    *ngIf="
                                                        item.get('online')?.value &&
                                                        item.get('offline')?.value &&
                                                        (item.get('online')?.dirty || item.get('online')?.touched)
                                                    "
                                                    class="text-red-400"
                                                >
                                                    <span>Không thể chọn cả hai trường: Online và Offline</span>
                                                </div>
                                            </app-form-item>
                                        </td>
                                        <td>
                                            <div class="tw-flex tw-flex-nowrap tw-gap-3">
                                                <button
                                                    [disabled]="mode === 'view'"
                                                    class="p-link tw-p-2 bg-red-300 hover:tw-bg-red-400 tw-text-white"
                                                    pTooltip="Hủy"
                                                    tooltipPosition="top"
                                                    type="button"
                                                    (click)="removeItem(rowIndex)"
                                                >
                                                    <span class="pi pi-times"></span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
                            <div class="tw-mt-3">
                                <p-button
                                    [disabled]="mode === 'view'"
                                    label="Thêm dòng"
                                    icon="pi pi-plus"
                                    severity="info"
                                    size="small"
                                    (click)="addItem()"
                                ></p-button>
                            </div>
                        </p-panel>
                    </div>
                </app-form>
            </app-form-item>
        </div>
    </div>

    <div class="tw-flex tw-gap-4 tw-justify-center tw-mt-4">
        <p-button
            *ngIf="
                (detailBor?.instructionInfo?.status === 1 ||
                    detailBor?.instructionInfo?.status === 4 ||
                    this.instructionId === 0 ||
                    isCreatingInstruction ||
                    !detailBor?.instructionInfo?.status) &&
                mode !== 'view'
            "
            label="Lưu"
            type="submit"
            [loading]="isSaving | async"
            (onClick)="handleSubmit($event)"
            loadingIcon="pi pi-spinner pi-spin"
        >
        </p-button>
        <button
            *ngIf="
                detailBor?.instructionInfo?.status === 1 ||
                detailBor?.instructionInfo?.status === 4 ||
                this.instructionId === 0 ||
                isCreatingInstruction ||
                !detailBor?.instructionInfo?.status
            "
            label="Gửi review"
            pButton
            type="button"
            [disabled]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            class="p-button-secondary"
            (click)="handlePreview()"
        ></button>
        <p-button
            *ngIf="
                !isCreatingInstruction &&
                detailBor?.instructionInfo?.status !== 1 &&
                detailBor?.instructionInfo?.status !== 4 &&
                this.instructionId !== 0 &&
                detailBor?.instructionInfo?.status
            "
            label="Phê duyệt"
            type="button"
            [disabled]="isApproving | async"
            [loading]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            (click)="handleComplete()"
        >
        </p-button>
        <button
            *ngIf="
                !isCreatingInstruction &&
                detailBor?.instructionInfo?.status !== 1 &&
                detailBor?.instructionInfo?.status !== 4 &&
                this.instructionId !== 0 &&
                detailBor?.instructionInfo?.status
            "
            label="Từ chối"
            pButton
            type="button"
            [disabled]="isApproving | async"
            loadingIcon="pi pi-spinner pi-spin"
            class="p-button-danger"
            (click)="handleReject()"
        ></button>
    </div>
</ng-container>
